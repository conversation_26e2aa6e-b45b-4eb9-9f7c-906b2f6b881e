/**
 * MEDICAL CODE SUGGESTIONS TESTS
 * 
 * Focused tests for ICD-10 and CPT code suggestion functionality
 * including diagnosis coding, procedure coding, and regional adaptations.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { clinicalDocumentationService } from '../../services/ClinicalDocumentationService';

describe('Medical Code Suggestions', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    clinicalDocumentationService.clearCaches();
  });

  describe('generateCodeSuggestions', () => {
    it('should generate ICD-10 and CPT code suggestions', async () => {
      const assessment = {
        primaryDiagnosis: {
          condition: 'hypertension',
          confidence: 'probable',
          evidenceLevel: 'B'
        },
        differentialDiagnoses: [
          {
            condition: 'diabetes',
            confidence: 'possible',
            evidenceLevel: 'C'
          }
        ]
      };

      const procedures = [
        {
          procedure: 'blood pressure check',
          indication: 'hypertension monitoring',
          provider: 'provider-456'
        }
      ];

      const culturalContext = {
        cultureCode: 'akan',
        country: 'GH'
      };

      const result = await clinicalDocumentationService.generateCodeSuggestions(
        assessment,
        procedures,
        culturalContext
      );

      expect(result.icd10).toBeDefined();
      expect(result.icd10.length).toBeGreaterThan(0);
      expect(result.cpt).toBeDefined();
      expect(result.cpt.length).toBeGreaterThan(0);

      // Check for hypertension ICD-10 code
      const hypertensionCode = result.icd10.find(code => code.code === 'I10');
      expect(hypertensionCode).toBeDefined();
      expect(hypertensionCode.description).toContain('hypertension');
    });

    it('should handle empty assessment and procedures', async () => {
      const emptyAssessment = {
        primaryDiagnosis: {
          condition: '',
          confidence: 'unknown',
          evidenceLevel: 'D'
        },
        differentialDiagnoses: []
      };

      const result = await clinicalDocumentationService.generateCodeSuggestions(
        emptyAssessment,
        [],
        undefined
      );

      expect(result.icd10).toBeDefined();
      expect(result.icd10.length).toBe(0);
      expect(result.cpt).toBeDefined();
      expect(result.cpt.length).toBe(0);
    });

    it('should suggest appropriate ICD-10 codes for common conditions', async () => {
      const assessment = {
        primaryDiagnosis: {
          condition: 'diabetes mellitus type 2',
          confidence: 'definite',
          evidenceLevel: 'A'
        },
        secondaryDiagnoses: [
          {
            condition: 'hypertension',
            confidence: 'established',
            evidenceLevel: 'A'
          }
        ]
      };

      const result = await clinicalDocumentationService.generateCodeSuggestions(
        assessment,
        [],
        { country: 'NG' }
      );

      expect(result.icd10.length).toBeGreaterThan(0);

      // Check for diabetes ICD-10 code
      const diabetesCode = result.icd10.find(code => code.code.startsWith('E11'));
      expect(diabetesCode).toBeDefined();

      // Check for hypertension ICD-10 code
      const hypertensionCode = result.icd10.find(code => code.code === 'I10');
      expect(hypertensionCode).toBeDefined();
    });

    it('should suggest CPT codes for procedures', async () => {
      const assessment = {
        primaryDiagnosis: {
          condition: 'chest pain',
          confidence: 'probable',
          evidenceLevel: 'B'
        }
      };

      const procedures = [
        {
          procedure: 'electrocardiogram',
          indication: 'chest pain evaluation',
          provider: 'provider-456'
        },
        {
          procedure: 'chest x-ray',
          indication: 'rule out pneumonia',
          provider: 'provider-456'
        }
      ];

      const result = await clinicalDocumentationService.generateCodeSuggestions(
        assessment,
        procedures,
        { country: 'KE' }
      );

      expect(result.cpt.length).toBeGreaterThan(0);

      // Check for ECG CPT code
      const ecgCode = result.cpt.find(code => code.description.toLowerCase().includes('electrocardiogram'));
      expect(ecgCode).toBeDefined();

      // Check for chest x-ray CPT code
      const xrayCode = result.cpt.find(code => code.description.toLowerCase().includes('chest'));
      expect(xrayCode).toBeDefined();
    });

    it('should handle infectious diseases common in Africa', async () => {
      const assessment = {
        primaryDiagnosis: {
          condition: 'malaria',
          confidence: 'probable',
          evidenceLevel: 'B'
        },
        differentialDiagnoses: [
          {
            condition: 'typhoid fever',
            confidence: 'possible',
            evidenceLevel: 'C'
          }
        ]
      };

      const procedures = [
        {
          procedure: 'malaria rapid test',
          indication: 'malaria diagnosis',
          provider: 'provider-456'
        }
      ];

      const result = await clinicalDocumentationService.generateCodeSuggestions(
        assessment,
        procedures,
        { country: 'NG', region: 'west_africa' }
      );

      expect(result.icd10.length).toBeGreaterThan(0);

      // Check for malaria ICD-10 code
      const malariaCode = result.icd10.find(code => code.code.startsWith('B5'));
      expect(malariaCode).toBeDefined();
    });

    it('should provide confidence scores for code suggestions', async () => {
      const assessment = {
        primaryDiagnosis: {
          condition: 'hypertension',
          confidence: 'definite',
          evidenceLevel: 'A'
        }
      };

      const result = await clinicalDocumentationService.generateCodeSuggestions(
        assessment,
        [],
        { country: 'GH' }
      );

      expect(result.icd10.length).toBeGreaterThan(0);

      const hypertensionCode = result.icd10.find(code => code.code === 'I10');
      expect(hypertensionCode).toBeDefined();
      expect(hypertensionCode.confidence).toBeDefined();
      expect(hypertensionCode.confidence).toBeGreaterThan(0);
      expect(hypertensionCode.confidence).toBeLessThanOrEqual(100);
    });

    it('should suggest modifier codes when appropriate', async () => {
      const assessment = {
        primaryDiagnosis: {
          condition: 'diabetes mellitus type 2 with complications',
          confidence: 'definite',
          evidenceLevel: 'A',
          complications: ['diabetic nephropathy', 'diabetic retinopathy']
        }
      };

      const result = await clinicalDocumentationService.generateCodeSuggestions(
        assessment,
        [],
        { country: 'ZA' }
      );

      expect(result.icd10.length).toBeGreaterThan(1);

      // Should suggest codes for diabetes and its complications
      const diabetesWithComplicationsCode = result.icd10.find(
        code => code.description.toLowerCase().includes('complication')
      );
      expect(diabetesWithComplicationsCode).toBeDefined();
    });

    it('should handle regional code variations', async () => {
      const assessment = {
        primaryDiagnosis: {
          condition: 'tuberculosis',
          confidence: 'probable',
          evidenceLevel: 'B'
        }
      };

      const procedures = [
        {
          procedure: 'sputum examination',
          indication: 'tuberculosis diagnosis',
          provider: 'provider-456'
        }
      ];

      const result = await clinicalDocumentationService.generateCodeSuggestions(
        assessment,
        procedures,
        { 
          country: 'ZA',
          region: 'southern_africa',
          healthcareSystem: 'public'
        }
      );

      expect(result.icd10.length).toBeGreaterThan(0);

      // Check for TB ICD-10 code
      const tbCode = result.icd10.find(code => code.code.startsWith('A15'));
      expect(tbCode).toBeDefined();

      // Should include regional considerations
      expect(result.metadata).toBeDefined();
      expect(result.metadata.regionalAdaptations).toBeDefined();
    });

    it('should validate code accuracy and completeness', async () => {
      const assessment = {
        primaryDiagnosis: {
          condition: 'essential hypertension',
          confidence: 'definite',
          evidenceLevel: 'A'
        },
        secondaryDiagnoses: [
          {
            condition: 'obesity',
            confidence: 'established',
            evidenceLevel: 'A'
          }
        ]
      };

      const result = await clinicalDocumentationService.generateCodeSuggestions(
        assessment,
        [],
        { country: 'KE' }
      );

      expect(result.icd10.length).toBeGreaterThan(0);

      // Validate code format
      result.icd10.forEach(code => {
        expect(code.code).toMatch(/^[A-Z]\d{2}(\.\d{1,2})?$/);
        expect(code.description).toBeDefined();
        expect(code.description.length).toBeGreaterThan(0);
        expect(code.confidence).toBeGreaterThan(0);
      });

      // Validate CPT codes if present
      if (result.cpt.length > 0) {
        result.cpt.forEach(code => {
          expect(code.code).toMatch(/^\d{5}$/);
          expect(code.description).toBeDefined();
          expect(code.description.length).toBeGreaterThan(0);
        });
      }
    });

    it('should handle multiple diagnoses with appropriate prioritization', async () => {
      const assessment = {
        primaryDiagnosis: {
          condition: 'acute myocardial infarction',
          confidence: 'definite',
          evidenceLevel: 'A'
        },
        secondaryDiagnoses: [
          {
            condition: 'diabetes mellitus type 2',
            confidence: 'established',
            evidenceLevel: 'A'
          },
          {
            condition: 'hypertension',
            confidence: 'established',
            evidenceLevel: 'A'
          }
        ]
      };

      const result = await clinicalDocumentationService.generateCodeSuggestions(
        assessment,
        [],
        { country: 'NG' }
      );

      expect(result.icd10.length).toBeGreaterThanOrEqual(3);

      // Primary diagnosis should have highest confidence
      const primaryCode = result.icd10.find(code => 
        code.description.toLowerCase().includes('myocardial')
      );
      expect(primaryCode).toBeDefined();
      expect(primaryCode.priority).toBe('primary');

      // Secondary diagnoses should be marked appropriately
      const secondaryCodes = result.icd10.filter(code => 
        code.priority === 'secondary'
      );
      expect(secondaryCodes.length).toBeGreaterThanOrEqual(2);
    });
  });
});
