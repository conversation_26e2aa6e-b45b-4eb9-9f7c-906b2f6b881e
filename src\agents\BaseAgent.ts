/**
 * BASE AGENT INTERFACE AND ABSTRACT CLASS
 * 
 * Defines the foundational structure for all AI agents in the VoiceHealth system.
 * Replaces the hardcoded agent configurations with proper object-oriented design.
 * 
 * FEATURES:
 * - Type-safe agent interface
 * - Capability-based agent selection
 * - Confidence scoring for agent handoffs
 * - HIPAA-compliant conversation handling
 * - Emergency protocol support
 * - Audit logging integration
 */

import type { ConversationMessage, ConversationContext } from '../types/memory';
import type { MemoryManager } from '../services/MemoryManager';
import type { ITool } from '../tools/BaseTool';

// Core agent types
export type AgentRole =
  | 'general_practitioner'
  | 'cardiologist'
  | 'nutritionist'
  | 'psychiatrist'
  | 'dermatologist'
  | 'neurologist'
  | 'emergency'
  | 'triage'
  | 'meta_agent'
  | 'education_specialist';

export type AgentCapability =
  | 'primary_care'
  | 'emergency_response'
  | 'specialist_consultation'
  | 'mental_health'
  | 'nutrition_planning'
  | 'diagnostic_assessment'
  | 'treatment_planning'
  | 'patient_education'
  | 'medication_management'
  | 'preventive_care'
  | 'chronic_disease_management'
  | 'goal_tracking'
  | 'conversation_steering'
  | 'progress_monitoring'
  | 'relevance_scoring'
  | 'cultural_adaptation'
  | 'health_literacy_assessment'
  | 'content_personalization'
  | 'visual_analysis'
  | 'emotional_intelligence';

export interface AgentRequest {
  sessionId: string;
  userMessage: string;
  conversationHistory: ConversationMessage[];
  patientContext?: PatientContext;
  urgencyLevel?: 'low' | 'medium' | 'high' | 'critical';
  requestedCapabilities?: AgentCapability[];
  assembledContext?: any; // From ContextAssemblyService
  emotionalContext?: import('../types/emotional').EmotionalContext; // Emotional intelligence context
}

export interface AgentResponse {
  agentId: string;
  agentName: string;
  content: string;
  confidence: number; // 0-1 scale
  reasoning?: string;
  suggestedHandoffs?: AgentHandoffSuggestion[];
  emergencyFlags?: EmergencyFlag[];
  followUpActions?: FollowUpAction[];
  metadata?: Record<string, any>;
}

export interface AgentHandoffSuggestion {
  targetAgentRole: AgentRole;
  reason: string;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  contextToTransfer: string;
  patientConsent?: boolean;
}

export interface EmergencyFlag {
  type: 'medical_emergency' | 'mental_health_crisis' | 'medication_reaction' | 'severe_symptoms';
  severity: 'medium' | 'high' | 'critical';
  description: string;
  recommendedAction: string;
  timeToResponse: number; // milliseconds, must be < 2000 for critical
}

export interface FollowUpAction {
  type: 'schedule_appointment' | 'medication_reminder' | 'test_results' | 'specialist_referral';
  description: string;
  timeframe: string;
  priority: 'low' | 'medium' | 'high';
}

export interface PatientContext {
  patientId?: string;
  userId?: string;
  age?: number;
  gender?: string;
  medicalHistory?: string[];
  currentMedications?: string[];
  allergies?: string[];
  emergencyContacts?: EmergencyContact[];
  preferredLanguage?: string;
  regionalContext?: {
    countryCode?: string;
    countryName?: string;
    region?: string;
    culturalConsiderations?: string[];
    [key: string]: any;
  };
}

export interface EmergencyContact {
  name: string;
  relationship: string;
  phone: string;
  isPrimary: boolean;
}

/**
 * Base Agent Interface
 * All agents must implement this interface
 */
export interface IAgent {
  // Core properties
  readonly id: string;
  readonly name: string;
  readonly role: AgentRole;
  readonly capabilities: AgentCapability[];
  readonly systemPrompt: string;
  readonly isActive: boolean;
  readonly tools: ITool[];

  // Core methods
  handleMessage(request: AgentRequest): Promise<AgentResponse>;
  canHandle(request: AgentRequest): boolean;
  getConfidenceScore(request: AgentRequest): number;

  // Tool methods
  getAvailableTools(): ITool[];
  canUseTool(toolId: string): boolean;

  // Lifecycle methods
  initialize(): Promise<void>;
  shutdown(): Promise<void>;

  // Health and monitoring
  healthCheck(): Promise<{ healthy: boolean; details: string }>;
  getPerformanceMetrics(): AgentPerformanceMetrics;
}

export interface AgentPerformanceMetrics {
  totalRequests: number;
  averageResponseTime: number;
  averageConfidence: number;
  successRate: number;
  handoffRate: number;
  emergencyDetectionRate: number;
  lastActivity: string;
}

/**
 * Abstract Base Agent Class
 * Provides common functionality for all agents
 */
export abstract class BaseAgent implements IAgent {
  public readonly id: string;
  public readonly name: string;
  public readonly role: AgentRole;
  public readonly capabilities: AgentCapability[];
  public readonly systemPrompt: string;
  public readonly isActive: boolean = true;
  public readonly tools: ITool[];

  protected memoryManager: MemoryManager;
  protected performanceMetrics: AgentPerformanceMetrics;

  constructor(
    id: string,
    name: string,
    role: AgentRole,
    capabilities: AgentCapability[],
    systemPrompt: string,
    memoryManager: MemoryManager,
    tools: ITool[] = []
  ) {
    this.id = id;
    this.name = name;
    this.role = role;
    this.capabilities = capabilities;
    this.systemPrompt = systemPrompt;
    this.memoryManager = memoryManager;
    this.tools = tools;
    
    this.performanceMetrics = {
      totalRequests: 0,
      averageResponseTime: 0,
      averageConfidence: 0,
      successRate: 0,
      handoffRate: 0,
      emergencyDetectionRate: 0,
      lastActivity: new Date().toISOString()
    };
  }

  /**
   * Abstract method - must be implemented by each agent
   */
  abstract handleMessage(request: AgentRequest): Promise<AgentResponse>;

  /**
   * Get available tools for this agent
   */
  getAvailableTools(): ITool[] {
    return [...this.tools];
  }

  /**
   * Check if agent can use a specific tool
   */
  canUseTool(toolId: string): boolean {
    return this.tools.some(tool => tool.id === toolId && tool.isActive);
  }

  /**
   * Execute a tool with the given request
   */
  protected async executeTool(toolId: string, request: any): Promise<any> {
    const tool = this.tools.find(t => t.id === toolId);
    if (!tool) {
      throw new Error(`Tool not found: ${toolId}`);
    }

    if (!tool.isActive) {
      throw new Error(`Tool is inactive: ${toolId}`);
    }

    return await tool.execute(request);
  }

  /**
   * Check if this agent can handle the request
   * Default implementation checks capabilities
   */
  canHandle(request: AgentRequest): boolean {
    // Check if agent has required capabilities
    if (request.requestedCapabilities) {
      return request.requestedCapabilities.some(cap => 
        this.capabilities.includes(cap)
      );
    }

    // Default: all agents can handle basic requests
    return true;
  }

  /**
   * Get confidence score for handling this request
   * Higher score = better match for the request
   */
  getConfidenceScore(request: AgentRequest): number {
    let score = 0.5; // Base confidence

    // Boost score if agent has requested capabilities
    if (request.requestedCapabilities) {
      const matchingCaps = request.requestedCapabilities.filter(cap => 
        this.capabilities.includes(cap)
      );
      score += (matchingCaps.length / request.requestedCapabilities.length) * 0.4;
    }

    // Adjust based on urgency and agent specialization
    if (request.urgencyLevel === 'critical' && this.role === 'emergency') {
      score += 0.3;
    }

    // Clamp between 0 and 1
    return Math.max(0, Math.min(1, score));
  }

  /**
   * Initialize the agent
   */
  async initialize(): Promise<void> {
    console.log(`🤖 Initializing agent: ${this.name} (${this.role})`);
    // Override in subclasses for specific initialization
  }

  /**
   * Shutdown the agent gracefully
   */
  async shutdown(): Promise<void> {
    console.log(`🛑 Shutting down agent: ${this.name} (${this.role})`);
    // Override in subclasses for cleanup
  }

  /**
   * Health check for the agent
   */
  async healthCheck(): Promise<{ healthy: boolean; details: string }> {
    try {
      // Basic health checks
      const memoryHealth = await this.memoryManager.healthCheck();
      
      if (!memoryHealth.healthy) {
        return {
          healthy: false,
          details: `Memory system unhealthy: ${memoryHealth.details}`
        };
      }

      return {
        healthy: true,
        details: `Agent ${this.name} is operational`
      };
    } catch (error) {
      return {
        healthy: false,
        details: `Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): AgentPerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * Update performance metrics after handling a request
   */
  protected updateMetrics(responseTime: number, confidence: number, hadHandoff: boolean, hadEmergency: boolean): void {
    this.performanceMetrics.totalRequests++;
    this.performanceMetrics.lastActivity = new Date().toISOString();
    
    // Update averages
    const total = this.performanceMetrics.totalRequests;
    this.performanceMetrics.averageResponseTime = 
      ((this.performanceMetrics.averageResponseTime * (total - 1)) + responseTime) / total;
    this.performanceMetrics.averageConfidence = 
      ((this.performanceMetrics.averageConfidence * (total - 1)) + confidence) / total;
    
    // Update rates
    if (hadHandoff) {
      this.performanceMetrics.handoffRate = 
        ((this.performanceMetrics.handoffRate * (total - 1)) + 1) / total;
    }
    
    if (hadEmergency) {
      this.performanceMetrics.emergencyDetectionRate = 
        ((this.performanceMetrics.emergencyDetectionRate * (total - 1)) + 1) / total;
    }
  }

  /**
   * Detect emergency situations in user messages
   */
  protected detectEmergencies(message: string): EmergencyFlag[] {
    const emergencyFlags: EmergencyFlag[] = [];
    const lowerMessage = message.toLowerCase();

    // Critical medical emergencies
    const criticalKeywords = [
      'chest pain', 'heart attack', 'stroke', 'can\'t breathe', 'unconscious',
      'severe bleeding', 'overdose', 'suicide', 'self harm'
    ];

    // High severity symptoms
    const highSeverityKeywords = [
      'severe pain', 'difficulty breathing', 'allergic reaction',
      'high fever', 'seizure', 'head injury'
    ];

    criticalKeywords.forEach(keyword => {
      if (lowerMessage.includes(keyword)) {
        emergencyFlags.push({
          type: 'medical_emergency',
          severity: 'critical',
          description: `Detected critical keyword: ${keyword}`,
          recommendedAction: 'Immediate emergency services contact required',
          timeToResponse: 1000 // 1 second for critical
        });
      }
    });

    highSeverityKeywords.forEach(keyword => {
      if (lowerMessage.includes(keyword)) {
        emergencyFlags.push({
          type: 'medical_emergency',
          severity: 'high',
          description: `Detected high severity symptom: ${keyword}`,
          recommendedAction: 'Urgent medical attention recommended',
          timeToResponse: 1500 // 1.5 seconds for high
        });
      }
    });

    return emergencyFlags;
  }
}

export default BaseAgent;
