/**
 * LAZY-<PERSON><PERSON><PERSON><PERSON> ROUTES FOR MEDICAL APPLICATION (REFACTORED)
 *
 * Main orchestrator for lazy loading routes that imports focused route modules:
 * - EmergencyRoutes: Critical emergency routes with preloading
 * - PatientRoutes: Patient-specific routes and dashboards
 * - ProviderRoutes: Healthcare provider routes and tools
 * - AuthRoutes: Authentication and role-based routing
 *
 * This file has been refactored into modular route files for better maintainability.
 * Individual route modules are located in src/components/routes/
 *
 * PERFORMANCE TARGETS:
 * - 40% bundle size reduction
 * - Emergency routes load < 100ms
 * - Role-based routes load < 500ms
 * - Offline fallbacks available
 */

import React from 'react';
import { createLazyComponent } from '../../utils/lazyLoading';
import type { UserRole } from '../../types/auth';

// Import modular route modules
import {
  EmergencyProtocols,
  EmergencyConsultation,
  CriticalVitals,
  OfflineEmergencyProtocols,
  preloadEmergencyRoutes,
  checkEmergencyRoutesAvailability
} from './EmergencyRoutes';

import {
  PatientDashboard,
  MedicalHistory,
  SymptomTracker,
  MedicationManager,
  AppointmentBooking,
  HealthMetrics,
  ProfileSettings,
  preloadPatientRoutes,
  getPatientRoutePriorities
} from './PatientRoutes';

import {
  ProviderDashboard,
  PatientManagement,
  MedicalRecords,
  ConsultationInterface,
  DiagnosisTools,
  ClinicalDecisionSupport,
  ReportsGenerator,
  preloadProviderRoutes,
  getProviderRoutePriorities,
  checkProviderRouteAccess
} from './ProviderRoutes';

import {
  LoginPage,
  RegisterPage,
  ForgotPassword,
  RoleBasedDashboard,
  WelcomeLanguageSelection,
  CountryRegionalSelection,
  preloadAuthRoutes,
  getDashboardForRole,
  requiresAuthentication
} from './AuthRoutes';

// =============================================================================
// RE-EXPORT ALL ROUTES FROM MODULAR FILES
// =============================================================================

// Emergency routes (re-exported from EmergencyRoutes.tsx)
export {
  EmergencyProtocols,
  EmergencyConsultation,
  CriticalVitals,
  OfflineEmergencyProtocols
} from './EmergencyRoutes';

// Patient routes (re-exported from PatientRoutes.tsx)
export {
  PatientDashboard,
  MedicalHistory,
  SymptomTracker,
  MedicationManager,
  AppointmentBooking,
  HealthMetrics,
  ProfileSettings
} from './PatientRoutes';

// Provider routes (re-exported from ProviderRoutes.tsx)
export {
  ProviderDashboard,
  PatientManagement,
  MedicalRecords,
  ConsultationInterface,
  DiagnosisTools,
  ClinicalDecisionSupport,
  ReportsGenerator
} from './ProviderRoutes';

// Authentication routes (re-exported from AuthRoutes.tsx)
export {
  LoginPage,
  RegisterPage,
  ForgotPassword,
  RoleBasedDashboard,
  WelcomeLanguageSelection,
  CountryRegionalSelection
} from './AuthRoutes';

// =============================================================================
// SHARED MEDICAL COMPONENTS
// =============================================================================

export const MedicalCharts = createLazyComponent(
  () => import('../charts/MedicalCharts').catch(() => ({
    default: () => (
      <div className="p-8 bg-gray-50 border border-gray-200 rounded">
        <h2 className="text-gray-800 font-bold">📊 Medical Charts</h2>
        <p className="text-gray-700 mt-2">Medical charts system is loading...</p>
      </div>
    )
  })),
  {
    priority: 'low' // Charts are heavy, load them last
  }
);

export const DataVisualization = createLazyComponent(
  () => import('../visualization/DataVisualization').catch(() => ({
    default: () => (
      <div className="p-8 bg-gray-50 border border-gray-200 rounded">
        <h2 className="text-gray-800 font-bold">📈 Data Visualization</h2>
        <p className="text-gray-700 mt-2">Data visualization tools are loading...</p>
      </div>
    )
  })),
  {
    priority: 'low'
  }
);

// =============================================================================
// UTILITY ROUTES
// =============================================================================

export const NotificationCenter = createLazyComponent(
  () => import('../notifications/NotificationCenter').catch(() => ({
    default: () => (
      <div className="p-8 bg-blue-50 border border-blue-200 rounded">
        <h2 className="text-blue-800 font-bold">🔔 Notification Center</h2>
        <p className="text-blue-700 mt-2">Notification system is loading...</p>
      </div>
    )
  })),
  {
    priority: 'normal'
  }
);

export const HelpCenter = createLazyComponent(
  () => import('../help/HelpCenter').catch(() => ({
    default: () => (
      <div className="p-8 bg-green-50 border border-green-200 rounded">
        <h2 className="text-green-800 font-bold">❓ Help Center</h2>
        <p className="text-green-700 mt-2">Help documentation is loading...</p>
      </div>
    )
  })),
  {
    priority: 'low'
  }
);

export const PrivacyPolicy = createLazyComponent(
  () => import('../legal/PrivacyPolicy').catch(() => ({
    default: () => (
      <div className="p-8 bg-gray-50 border border-gray-200 rounded">
        <h2 className="text-gray-800 font-bold">🔒 Privacy Policy</h2>
        <p className="text-gray-700 mt-2">Privacy policy is loading...</p>
      </div>
    )
  })),
  {
    priority: 'low'
  }
);

// =============================================================================
// ROUTE MANAGEMENT UTILITIES
// =============================================================================

/**
 * Preload routes based on user role
 */
export function preloadRoleBasedRoutes(userRole: UserRole): void {
  console.log(`🚀 Preloading routes for role: ${userRole}`);

  switch (userRole) {
    case 'patient':
      preloadPatientRoutes();
      break;
    case 'healthcare_provider':
      preloadProviderRoutes();
      break;
    case 'emergency_responder':
      preloadEmergencyRoutes();
      break;
    case 'admin':
      // Admin routes would be preloaded here
      break;
    default:
      preloadAuthRoutes();
  }
}

/**
 * Get route loading priority based on user role and current route
 */
export function getRouteLoadingPriority(userRole: UserRole, routeName: string): 'low' | 'normal' | 'high' | 'emergency' {
  // Emergency routes always have emergency priority
  if (routeName.includes('emergency') || routeName.includes('critical')) {
    return 'emergency';
  }

  // Dashboard routes have high priority
  if (routeName.includes('dashboard')) {
    return 'high';
  }

  // Medical data routes have normal priority
  if (routeName.includes('medical') || routeName.includes('consultation')) {
    return 'normal';
  }

  // Everything else has low priority
  return 'low';
}

/**
 * Initialize all route modules
 */
export async function initializeRoutes(): Promise<void> {
  console.log('🔧 Initializing route modules...');

  try {
    // Check emergency routes availability first
    const emergencyAvailable = await checkEmergencyRoutesAvailability();
    if (!emergencyAvailable) {
      console.warn('⚠️ Emergency routes not fully available');
    }

    // Preload emergency routes immediately
    preloadEmergencyRoutes();

    console.log('✅ Route modules initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize route modules:', error);
  }
}

/**
 * Get all available routes by category
 */
export function getRoutesByCategory() {
  return {
    emergency: ['EmergencyProtocols', 'EmergencyConsultation', 'CriticalVitals'],
    patient: ['PatientDashboard', 'MedicalHistory', 'SymptomTracker', 'MedicationManager'],
    provider: ['ProviderDashboard', 'PatientManagement', 'ConsultationInterface', 'MedicalRecords'],
    auth: ['LoginPage', 'RegisterPage', 'RoleBasedDashboard'],
    shared: ['MedicalCharts', 'DataVisualization', 'NotificationCenter', 'HelpCenter']
  };
}

export const ConsultationInterface = createLazyComponent(
  () => import('../consultation/ConsultationInterface').catch(() => ({ default: () => <div>Consultation Interface Placeholder</div> })),
  {
    priority: 'high',
    requiredRole: 'healthcare_provider'
  }
);

export const MedicalRecords = createLazyComponent(
  () => import('../medical/MedicalRecords').catch(() => ({ default: () => <div>Medical Records Placeholder</div> })),
  {
    priority: 'normal',
    requiredRole: 'healthcare_provider'
  }
);

export const DiagnosisTools = createLazyComponent(
  () => import('../provider/DiagnosisTools').catch(() => ({ default: () => <div>Diagnosis Tools Placeholder</div> })),
  {
    priority: 'normal',
    requiredRole: 'healthcare_provider'
  }
);

// =============================================================================
// ADMIN ROUTES (LAZY LOADED)
// =============================================================================

export const AdminDashboard = createLazyComponent(
  () => import('../dashboard/AdminDashboard'),
  { 
    priority: 'normal',
    requiredRole: 'admin'
  }
);

export const UserManagement = createLazyComponent(
  () => import('../admin/UserManagement'),
  { 
    priority: 'normal',
    requiredRole: 'admin'
  }
);

export const SystemSettings = createLazyComponent(
  () => import('../admin/SystemSettings'),
  { 
    priority: 'normal',
    requiredRole: 'admin'
  }
);

export const AuditLogs = createLazyComponent(
  () => import('../admin/AuditLogs'),
  { 
    priority: 'normal',
    requiredRole: 'admin'
  }
);

export const AnalyticsDashboard = createLazyComponent(
  () => import('../analytics/AnalyticsDashboard'),
  { 
    priority: 'low',
    requiredRole: 'admin'
  }
);

// =============================================================================
// SHARED MEDICAL COMPONENTS (LAZY LOADED)
// =============================================================================

export const HealthMetrics = createLazyComponent(
  () => import('../medical/HealthMetrics'),
  { 
    priority: 'normal'
  }
);

export const MedicalCharts = createLazyComponent(
  () => import('../charts/MedicalCharts'),
  { 
    priority: 'low' // Charts are heavy, load them last
  }
);

export const ReportsGenerator = createLazyComponent(
  () => import('../reports/ReportsGenerator'),
  { 
    priority: 'low'
  }
);

export const DataVisualization = createLazyComponent(
  () => import('../visualization/DataVisualization'),
  { 
    priority: 'low'
  }
);

// =============================================================================
// ROLE-BASED DASHBOARD COMPONENT
// =============================================================================

export const RoleBasedDashboard = createRoleBasedLazyComponent(
  {
    'patient': () => import('../dashboard/PatientDashboard'),
    'healthcare_provider': () => import('../dashboard/ProviderDashboard'),
    'admin': () => import('../dashboard/AdminDashboard'),
    'emergency_responder': () => import('../dashboard/PatientDashboard') // Use PatientDashboard as fallback for emergency_responder
  },
  () => import('../dashboard/PatientDashboard'), // Default fallback
  {
    priority: 'high',
    errorFallback: ({ error, resetErrorBoundary }) => (
      <div className="p-8 bg-yellow-100 border border-yellow-300 rounded">
        <h2 className="text-yellow-800 font-bold">Dashboard Loading Error</h2>
        <p className="text-yellow-700 mt-2">Failed to load role-specific dashboard.</p>
        <p className="text-yellow-600 text-sm mt-1">Error: {error.message}</p>
        <button
          onClick={resetErrorBoundary}
          className="mt-4 px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
        >
          Retry Dashboard
        </button>
      </div>
    )
  }
);

// =============================================================================
// AUTHENTICATION ROUTES (LAZY LOADED)
// =============================================================================

export const LoginPage = createLazyComponent(
  () => import('../auth/LoginPage'),
  { 
    priority: 'high' // Authentication is important
  }
);

export const RegisterPage = createLazyComponent(
  () => import('../auth/RegisterPage'),
  { 
    priority: 'normal'
  }
);

export const ForgotPassword = createLazyComponent(
  () => import('../auth/ForgotPassword'),
  { 
    priority: 'normal'
  }
);

// =============================================================================
// UTILITY ROUTES (LAZY LOADED)
// =============================================================================

export const ProfileSettings = createLazyComponent(
  () => import('../profile/ProfileSettings'),
  { 
    priority: 'normal'
  }
);

export const NotificationCenter = createLazyComponent(
  () => import('../notifications/NotificationCenter'),
  { 
    priority: 'normal'
  }
);

export const HelpCenter = createLazyComponent(
  () => import('../help/HelpCenter'),
  { 
    priority: 'low'
  }
);

export const PrivacyPolicy = createLazyComponent(
  () => import('../legal/PrivacyPolicy'),
  { 
    priority: 'low'
  }
);

// =============================================================================
// OFFLINE FALLBACK COMPONENTS
// =============================================================================

export const OfflineDashboard = createLazyComponent(
  () => import('../offline/OfflineDashboard'),
  { 
    priority: 'high',
    preload: true // Preload for offline scenarios
  }
);

export const OfflineMedicalData = createLazyComponent(
  () => import('../offline/OfflineMedicalData'),
  { 
    priority: 'high',
    preload: true
  }
);

// =============================================================================
// ROUTE PRELOADING UTILITIES
// =============================================================================

/**
 * Preload routes based on user role
 */
export function preloadRoleBasedRoutes(userRole: UserRole): void {
  const roleRoutes: Partial<Record<UserRole, React.ComponentType<any>[]>> = {
    'patient': [PatientDashboard, MedicalHistory, SymptomTracker, MedicationManager],
    'healthcare_provider': [ProviderDashboard, PatientManagement, ConsultationInterface, MedicalRecords],
    'admin': [AdminDashboard],
    'emergency_responder': [EmergencyProtocols, EmergencyConsultation, CriticalVitals]
  };

  const routes = roleRoutes[userRole] || roleRoutes['patient'];
  
  // Preload role-specific routes
  routes.forEach(RouteComponent => {
    // The components are already lazy-loaded, this just triggers the import
    try {
      // This would trigger the lazy loading
      React.createElement(RouteComponent);
    } catch (error) {
      console.warn(`Failed to preload route for role ${userRole}:`, error);
    }
  });
}

/**
 * Get route loading priority based on user role and current route
 */
export function getRouteLoadingPriority(userRole: UserRole, routeName: string): 'low' | 'normal' | 'high' | 'emergency' {
  // Emergency routes always have emergency priority
  if (routeName.includes('emergency') || routeName.includes('critical')) {
    return 'emergency';
  }

  // Dashboard routes have high priority
  if (routeName.includes('dashboard')) {
    return 'high';
  }

  // Medical data routes have normal priority
  if (routeName.includes('medical') || routeName.includes('consultation')) {
    return 'normal';
  }

  // Everything else has low priority
  return 'low';
}
